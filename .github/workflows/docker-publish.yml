name: Build and Publish Docker Images

on:
  push:
    branches:
      - main
  workflow_dispatch:

permissions:
  contents: read
  packages: write

env:
  REGISTRY: ghcr.io
  IMAGE_NAMESPACE: ${{ github.repository_owner }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - service: api-gateway
            dockerfile: Dockerfile.api-gateway
          - service: assessment-engine
            dockerfile: Dockerfile.assessment-engine
          - service: blob-ms
            dockerfile: Dockerfile.blob-ms
          - service: document-ms
            dockerfile: Dockerfile.document-ms
          - service: user-service
            dockerfile: Dockerfile.user-service
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ env.IMAGE_NAMESPACE }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push ${{ matrix.service }} image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ${{ matrix.dockerfile }}
          push: true
          tags: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAMESPACE }}/aptitest-${{ matrix.service }}:latest
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAMESPACE }}/aptitest-${{ matrix.service }}:${{ github.sha }}
          labels: |
            org.opencontainers.image.source=${{ github.repository }}
            org.opencontainers.image.revision=${{ github.sha }}
