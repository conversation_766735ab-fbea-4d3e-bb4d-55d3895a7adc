import { Module } from '@nestjs/common';
import { SharedModule } from '../../../libs/shared/src/shared.module';
import { ConfigModule } from '@nestjs/config';
import { AssessmentEngineMsController } from './controller/assessment-engine.controller';
import { TestController } from './controller/test.controller';
import { AssessmentEngineService } from './services/assessment-engine.service';
import { TestService } from './services/test.service';
import { AssessmentRepository } from '@app/shared/assessment-engine/repository/assessment.repository';
import { TestResultRepository } from '@app/shared/assessment-engine/repository/test-result.repository';
import { TestRepository } from '@app/shared/assessment-engine/repository/test.repository';
import { QueueModule } from '@app/shared/queue/queue.module';
import { DatabaseModule } from '../../../libs/shared/src/database/database.module';
import { AptiTest } from '@app/shared/assessment-engine/entities/test.entity';
import { Assessment } from '@app/shared/assessment-engine/entities/assessment.entity';
import { TestResult } from '@app/shared/assessment-engine/entities/test-result.entity';
import { AptiQuestion } from '@app/shared/assessment-engine/entities/question.entity';
import { AptiOption } from '@app/shared/assessment-engine/entities/option.entity';
import { TestSession } from '@app/shared/assessment-engine/entities/test-session.entity';
import { TestSchedule } from '@app/shared/assessment-engine/entities/test-schedule.entity';
import { TestSessionRepository } from '@app/shared/assessment-engine/repository/test-session.repository';
import { TestSessionController } from './controller/test-session.controller';
import { TestSessionService } from './services/test-session.service';
import { QuestionRepository } from '@app/shared/assessment-engine/repository/question.repository';
import { AnswerEventRepository } from '@app/shared/assessment-engine/repository/answer-event.repository';
import { AnswerEventEntity } from '@app/shared/assessment-engine/entities/answer-event.entity';
import { TestScheduleRepository } from '@app/shared/assessment-engine/repository/test-schedule.repository';
import { ProctorEventEntity } from '@app/shared/assessment-engine/entities/proctor-event.entity';
import { ProctorEventRepository } from '@app/shared/assessment-engine/repository/proctor-event.repository';
import { TestScheduleService } from './services/test-schedule.service';
import { TestScheduleController } from './controller/test-schedule.controller';

@Module({
  imports: [
    SharedModule,
    QueueModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: './.env',
    }),
    DatabaseModule.forFeature([
      AptiTest,
      Assessment,
      TestResult,
      AptiQuestion,
      AptiOption,
      TestSession,
      AnswerEventEntity,
      ProctorEventEntity,
      TestSchedule,
    ]),
  ],
  controllers: [AssessmentEngineMsController, TestController, TestSessionController, TestScheduleController],
  providers: [
    AssessmentEngineService,
    TestService,
    TestRepository,
    AssessmentRepository,
    TestResultRepository,
    TestSessionRepository,
    AnswerEventRepository,
    QuestionRepository,
    TestSessionService,
    TestScheduleRepository,
    ProctorEventRepository,
    TestScheduleService,
  ],
})
export class AssessmentEngineMsModule {}
